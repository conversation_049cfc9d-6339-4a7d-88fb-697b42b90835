/* Container and Row Overrides with Modern Style */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container-fluid {
    padding: 20px;
    margin: 0;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.registration-row {
    display: flex;
    width: 100%;
    margin: 0;
    
    align-items: flex-start;
}

/* Column Styles */
.col-md-4, .col-md-8 {
    padding: 0;
    margin-top: 0;
}

/* Card Styles */
.card {
    background: white;
    margin-top: 0;
    border: none;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.card-header {
    background: white;
    border-radius: 20px 20px 0 0 !important;
    border-bottom: 1px solid #e2e8f0;
    padding: 20px;
}

.card-header h4 {
    color: #2d3748;
    font-weight: 600;
    margin: 0;
}

.card-header i {
    color: #667eea;
    margin-right: 10px;
}

.card-body {
    padding: 30px;
}

/* Form Controls */
.form-control {
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 12px 15px;
    height: auto;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    box-shadow: none;
    border-color: #667eea;
}

.form-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 500;
    transition: transform 0.2s;
}

.btn-primary:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.btn-primary:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Users Table */
.users-table {
    margin-top: 0;
    background: white;
    border-radius: 15px;
}

.table th {
    border-top: none;
    color: #718096;
    font-weight: 600;
    padding: 15px;
}

.table td {
    padding: 15px;
    vertical-align: middle;
}

/* User Badge Styles */
.user-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.badge-admin {
    background: linear-gradient(135deg, #ff6b6b 0%, #dc3545 100%);
    color: white;
}

.badge-pm {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.badge-creative {
    background: linear-gradient(135deg, #17a2b8 0%, #4facfe 100%);
    color: white;
}

.badge-editorial {
    background: linear-gradient(135deg, #ffc107 0%, #f7d794 100%);
    color: #2d3748;
}

.badge-basic {
    background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
    color: white;
}

/* Action buttons styling similar to tickets.html */
.action-buttons {
    white-space: nowrap;
    display: flex;
    justify-content: center;
}

.action-buttons .btn-link {
    padding: 4px 8px;
    margin: 0 2px;
    background: transparent;
    border: none;
}

.action-buttons .btn-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.action-buttons i {
    font-size: 1.1rem;
}

/* Icon-specific colors */
.action-buttons .fa-edit {
    color: #007bff;
}

.action-buttons .fa-trash {
    color: #dc3545;
}

/* Hover effects */
.action-buttons .btn-link:hover .fa-edit {
    color: #0056b3;
}

.action-buttons .btn-link:hover .fa-trash {
    color: #bd2130;
}

/* Add animation for delete action */
.action-buttons .fa-trash {
    transition: transform 0.2s ease;
}

.action-buttons .btn-link:hover .fa-trash {
    transform: scale(1.1);
}

/* Modal styling */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background-color: white;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 20px 20px 0 0;
    padding: 20px;
}

.modal-footer {
    background-color: white;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 20px 20px;
    padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .registration-row {
        flex-direction: column;
    }
    
    .col-md-4, .col-md-8 {
        width: 100%;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .container-fluid {
        padding: 10px;
    }
}

/* Form read-only state */
.form-control:read-only {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

/* Table Responsiveness */
.table-responsive {
    margin: 0;
    padding: 0;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

/* Top Bar Styles */
.top-bar-registration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #2c3e50;
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.left-section, .right-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.home-button {
    background-color: transparent;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.home-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    opacity: 0.8;
}

/* Adjust container padding to account for top bar */
.container-fluid {
    padding-top: 70px !important;
}

/* Category selector styles */
#categorySelector {
    width: auto;
    min-width: 120px;
}

/* Category accordion styles */
.accordion .card-header {
    padding: 0.5rem 1rem;
}

.accordion .btn-link {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 0;
}

.accordion .btn-link:hover,
.accordion .btn-link:focus {
    text-decoration: none;
}

/* Category tables */
.accordion .table {
    margin-bottom: 0;
}

.accordion .table th,
.accordion .table td {
    padding: 0.5rem 0.75rem;
}

/* Category summary card */
#category-summary .card {
    margin-bottom: 1.5rem;
}

#category-summary .card-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

/* Make the badges more visible */
.badge {
    font-size: 85%;
    padding: 0.35em 0.65em;
}

/* Sortable table header styles */
th[onclick] {
    user-select: none;
    transition: background-color 0.2s ease;
}

th[onclick]:hover {
    background-color: #f8f9fa;
}

#empId-header {
    position: relative;
}

#empId-sort-icon {
    margin-left: 5px;
    color: #6c757d;
    font-size: 0.8em;
}
