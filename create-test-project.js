const mongoose = require('mongoose');
const models = require('./models');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/wipautomation', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function createTestProject() {
  try {
    console.log('🔧 Creating test project 12345...');
    
    // Create the project
    const project = new models.Project({
      _id: '12345',
      title: 'Shekhar_Business Intelligence Simplified',
      clientName: 'Test Client',
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      projectManager: 'Test Manager',
      levels: '2',
      units: '3',
      status: 'In Progress'
    });
    
    await project.save();
    console.log('✅ Project created successfully');
    
    // Create project status for Level 1
    const projectStatus1 = new models.ProjectStatus({
      projectId: '12345-level-1',
      statusData: {
        headers: ['Level 1 Heading', 'Column 1'],
        rows: [
          ['Unit 1', 'Yet to Start'],
          ['Unit 2', 'In Progress'],
          ['Unit 3', 'Completed']
        ]
      }
    });
    
    await projectStatus1.save();
    console.log('✅ Project status for Level 1 created');
    
    // Create project status for Level 2
    const projectStatus2 = new models.ProjectStatus({
      projectId: '12345-level-2',
      statusData: {
        headers: ['Level 2 Heading', 'Column 1'],
        rows: [
          ['Unit 1', 'Yet to Start'],
          ['Unit 2', 'Yet to Start'],
          ['Unit 3', 'Yet to Start']
        ]
      }
    });
    
    await projectStatus2.save();
    console.log('✅ Project status for Level 2 created');
    
    console.log('\n🎉 Test project setup completed!');
    console.log('You can now test the level heading editing functionality.');
    
  } catch (error) {
    console.error('❌ Error creating test project:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the creation
createTestProject();
