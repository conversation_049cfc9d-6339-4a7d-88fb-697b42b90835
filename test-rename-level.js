const mongoose = require('mongoose');
const models = require('./models');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/wipautomation', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testRenameLevel() {
  try {
    console.log('🔧 Testing level rename functionality...');
    
    // Wait for connection to be ready
    await new Promise((resolve, reject) => {
      mongoose.connection.once('open', resolve);
      mongoose.connection.once('error', reject);
    });
    
    // Find the current Level 2 document
    const currentDoc = await models.ProjectStatus.findOne({ 
      projectId: '12345-level-2' 
    });
    
    if (!currentDoc) {
      console.log('❌ Level 2 document not found');
      return;
    }
    
    console.log('📋 Current document:', {
      projectId: currentDoc.projectId,
      headers: currentDoc.statusData.headers
    });
    
    // Simulate the rename operation
    const newHeading = 'Grade 7';
    const baseProjectId = '12345';
    const newProjectId = `${baseProjectId}-${newHeading}`;
    
    console.log(`🔄 Renaming to: ${newProjectId}`);
    
    // Create new document with updated projectId
    const newDoc = new models.ProjectStatus({
      projectId: newProjectId,
      statusData: {
        headers: [newHeading, ...currentDoc.statusData.headers.slice(1)],
        rows: currentDoc.statusData.rows
      }
    });
    
    await newDoc.save();
    console.log('✅ New document created:', newDoc.projectId);
    
    // Delete the old document
    await models.ProjectStatus.deleteOne({ projectId: currentDoc.projectId });
    console.log('✅ Old document deleted:', currentDoc.projectId);
    
    // Also update the WIP data
    const wipResult = await mongoose.connection.db.collection('projectwip').updateOne(
      { projectId: '12345-level-2' },
      { $set: { projectId: newProjectId } }
    );
    
    if (wipResult.modifiedCount > 0) {
      console.log('✅ WIP data updated');
    } else {
      console.log('⚠️ No WIP data found to update');
    }
    
    console.log('\n🎉 Level rename test completed!');
    console.log('You should now see "Grade 7" instead of "Level 2 Heading"');
    
  } catch (error) {
    console.error('❌ Error testing level rename:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the test
testRenameLevel();
