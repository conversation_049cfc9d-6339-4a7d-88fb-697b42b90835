const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/wipautomation');

async function checkWIPData() {
  try {
    console.log('🔍 Checking current data...');

    // Wait for connection
    await new Promise((resolve, reject) => {
      mongoose.connection.once('open', resolve);
      mongoose.connection.once('error', reject);
    });

    // Check ProjectStatus documents
    console.log('\n📋 ProjectStatus documents:');
    const projectStatuses = await mongoose.connection.db.collection('projectstatuses').find({
      projectId: { $regex: '^12345', $options: 'i' }
    }).sort({ projectId: 1 }).toArray();

    projectStatuses.forEach((status, index) => {
      console.log(`${index + 1}. ProjectId: ${status.projectId}`);
      console.log(`   Headers: ${JSON.stringify(status.statusData.headers)}`);
    });

    // Check WIP data
    console.log('\n📊 WIP documents:');
    const wipData = await mongoose.connection.db.collection('projectwip').find({
      projectId: { $regex: '^12345', $options: 'i' }
    }).sort({ projectId: 1 }).toArray();

    wipData.forEach((wip, index) => {
      console.log(`${index + 1}. ProjectId: ${wip.projectId}`);
      console.log(`   Column Counts:`, Object.keys(wip.columnCounts || {}));
    });

    // Check for mismatches
    console.log('\n🔍 Checking for mismatches:');
    const statusIds = projectStatuses.map(s => s.projectId);
    const wipIds = wipData.map(w => w.projectId);

    const statusOnly = statusIds.filter(id => !wipIds.includes(id));
    const wipOnly = wipIds.filter(id => !statusIds.includes(id));

    if (statusOnly.length > 0) {
      console.log('⚠️ ProjectStatus documents without WIP data:', statusOnly);
    }
    if (wipOnly.length > 0) {
      console.log('⚠️ WIP documents without ProjectStatus data:', wipOnly);
    }

  } catch (error) {
    console.error('❌ Error checking data:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the check
checkWIPData();
