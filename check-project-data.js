const mongoose = require('mongoose');
const ProjectStatus = require('./models/ProjectStatus');
const models = require('./models');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/wipautomation', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function checkProjectData() {
  try {
    console.log('🔍 Checking all project status data...');

    // Find all project statuses
    const allStatuses = await ProjectStatus.find({}).sort({ projectId: 1 });
    console.log(`📊 Found ${allStatuses.length} total project status documents:`);

    allStatuses.forEach((status, index) => {
      console.log(`\n${index + 1}. ProjectId: ${status.projectId}`);
      console.log(`   Headers: ${JSON.stringify(status.statusData.headers)}`);
      console.log(`   Rows count: ${status.statusData.rows.length}`);
      console.log(`   Created: ${status.createdAt}`);
      console.log(`   Updated: ${status.updatedAt}`);
    });

    // Now specifically check for project 12345
    console.log('\n🔍 Checking specifically for project 12345...');
    const project12345Statuses = await ProjectStatus.find({
      projectId: { $regex: '^12345', $options: 'i' }
    }).sort({ projectId: 1 });

    console.log(`📊 Found ${project12345Statuses.length} project status documents for 12345:`);

    project12345Statuses.forEach((status, index) => {
      console.log(`\n${index + 1}. ProjectId: ${status.projectId}`);
      console.log(`   Headers: ${JSON.stringify(status.statusData.headers)}`);
      console.log(`   Rows count: ${status.statusData.rows.length}`);
    });

    // Also check for any duplicates
    const projectIds = allStatuses.map(s => s.projectId);
    const duplicates = projectIds.filter((id, index) => projectIds.indexOf(id) !== index);

    if (duplicates.length > 0) {
      console.log(`\n⚠️  Found duplicates: ${duplicates.join(', ')}`);
    } else {
      console.log(`\n✅ No duplicates found`);
    }

    // Check if project 12345 exists in the projects collection
    console.log('\n🔍 Checking if project 12345 exists in projects collection...');
    const project = await models.Project.findOne({ _id: '12345' });
    if (project) {
      console.log(`✅ Project found: ${project.title}`);
      console.log(`   Levels: ${project.levels}`);
      console.log(`   Units: ${project.units}`);
    } else {
      console.log(`❌ Project 12345 not found in projects collection`);

      // Check what projects do exist
      const allProjects = await models.Project.find({}).limit(5);
      console.log(`\n📊 Found ${allProjects.length} projects (showing first 5):`);
      allProjects.forEach((proj, index) => {
        console.log(`${index + 1}. ID: ${proj._id}, Title: ${proj.title}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking project data:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the check
checkProjectData();
