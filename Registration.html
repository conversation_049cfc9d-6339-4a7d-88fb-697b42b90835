<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Management</title>
    <link rel="stylesheet" href="styles/style.css?v=2" />
    <link rel="stylesheet" href="styles/Registration.css" />
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/luxon@3/build/global/luxon.min.js"></script>
</head>
<body>
    <div class="container-fluid">
        <header class="top-bar-registration">
            <div class="left-section">
                <button onclick="goHome()" class="home-button">
                    <i class="fas fa-home"></i> Home
                </button>
            </div>
            <div class="right-section">
                <div class="user-info">
                    <div class="user-photo-container">
                        <img src="https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp" alt="User Photo" class="user-photo" onerror="this.onerror=null;this.src='https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';" />
                        <div class="user-dropdown-content">
                            <a href="/user-info.html" onclick="openSettings()"><i class="fas fa-cog"></i> Settings</a>
                        </div>
                    </div>
                    <div class="user-details">
                        <span class="user-name">Welcome, User</span>
                        <span class="user-role">Role</span>
                    </div>
                </div>
                <button onclick="logout()" class="logout-button">Logout</button>
            </div>
        </header>
        
        <div class="row">
            <!-- Left side: Existing Users Table (now takes 8 columns) -->
            <div class="col-md-8">
                <div class="card users-table">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-users"></i> Existing Users</h4>
                        <div>
                            <button class="btn btn-primary mr-2" data-toggle="modal" data-target="#addUserModal">
                                <i class="fas fa-user-plus"></i> Add New User
                            </button>
                            <button class="btn btn-secondary" data-toggle="modal" data-target="#bulkImportModal">
                                <i class="fas fa-file-import"></i> Bulk Import
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Emp. ID</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Date of Birth</th>
                                        <th>Phone</th>
                                        <th>Location</th>
                                        <th>Department</th>
                                        <th>Position</th>
                                        <th>Join Date</th>
                                        <th>Role</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="users-table-body">
                                    <!-- Users will be dynamically loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right side: Users by Category (takes 4 columns) -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0"><i class="fas fa-user-tag"></i> Users by Category</h4>
                        <div class="form-group mb-0">
                            <select id="categorySelector" class="form-control form-control-sm">
                                <option value="role">Role</option>
                                <option value="location">Location</option>
                                <option value="department">Department</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="category-summary" class="mb-4">
                            <!-- Category summary counts will be loaded here -->
                        </div>
                        
                        <!-- Accordion for category-based user lists -->
                        <div class="accordion" id="categoryAccordion">
                            <!-- Category sections will be dynamically loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS and dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    
    <script>
        // Check if user is admin on page load
        document.addEventListener('DOMContentLoaded', function() {
            const userRole = localStorage.getItem('role');
            if (userRole !== 'Admin') {
                alert('Access Denied. Only administrators can access this page.');
                window.location.href = '/index.html';
                return;
            }
            loadUsers();
            
            // Add event listener for bulk import form
            const bulkImportForm = document.getElementById('bulk-import-form');
            if (bulkImportForm) {
                bulkImportForm.addEventListener('submit', async function(event) {
                    event.preventDefault();
                    
                    const formData = new FormData();
                    const fileInput = document.getElementById('userFile');
                    
                    if (fileInput.files.length === 0) {
                        alert('Please select a file to upload');
                        return;
                    }
                    
                    formData.append('userFile', fileInput.files[0]);
                    
                    try {
                        const response = await fetch('/api/users/import', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('token')}`
                            },
                            body: formData
                        });
                        
                        const result = await response.json();
                        
                        if (response.ok) {
                            // Close the bulk import modal
                            $('#bulkImportModal').modal('hide');
                            
                            // Display results in modal
                            document.getElementById('import-success').textContent = 
                                `Successfully imported ${result.results.length} users`;
                            
                            const importedUsersTable = document.getElementById('imported-users-table');
                            importedUsersTable.innerHTML = '';
                            
                            result.results.forEach(user => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${user.empId}</td>
                                    <td>${user.username}</td>
                                `;
                                importedUsersTable.appendChild(row);
                            });
                            
                            // Show errors if any
                            if (result.errors && result.errors.length > 0) {
                                const errorList = document.getElementById('error-list');
                                errorList.innerHTML = '';
                                
                                result.errors.forEach(error => {
                                    const li = document.createElement('li');
                                    li.textContent = `Row with empId "${error.row.empId}": ${error.error}`;
                                    errorList.appendChild(li);
                                });
                                
                                document.getElementById('import-errors').style.display = 'block';
                            } else {
                                document.getElementById('import-errors').style.display = 'none';
                            }
                            
                            // Show results modal
                            $('#importResultsModal').modal('show');
                            
                            // Reset form
                            document.getElementById('bulk-import-form').reset();
                            
                            // Reload users list
                            loadUsers();
                        } else {
                            alert(`Import failed: ${result.message}`);
                        }
                    } catch (error) {
                        console.error('Error importing users:', error);
                        alert('Error importing users');
                    }
                });
            } else {
                console.error('Bulk import form not found in the DOM');
            }
        });

        // Load existing users
        async function loadUsers() {
            try {
                const response = await fetch('/api/users', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Failed to fetch users');
                }

                const users = await response.json();
                const tableBody = document.getElementById('users-table-body');
                tableBody.innerHTML = '';
                
                users.forEach(user => {
                    const row = document.createElement('tr');
                    const roleBadgeClass = getRoleBadgeClass(user.role);
                    
                    // Format the dates
                    const dob = user.dob ? new Date(user.dob).toLocaleDateString() : 'N/A';
                    const joinDate = user.joinDate ? new Date(user.joinDate).toLocaleDateString() : 'N/A';
                    
                    row.innerHTML = `
                        <td>${user.empId || 'N/A'}</td>
                        <td>${user.username || 'N/A'}</td>
                        <td>${user.email || 'N/A'}</td>
                        <td>${dob}</td>
                        <td>${user.phone || 'N/A'}</td>
                        <td>${user.location || 'N/A'}</td>
                        <td>${user.department || 'N/A'}</td>
                        <td>${user.position || 'N/A'}</td>
                        <td>${formatDateCST(user.joinDate)}</td>
                        <td><span class="badge ${roleBadgeClass}">${user.role || 'N/A'}</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-link" onclick="editUser('${user._id}', '${user.username}', '${user.role}', '${user.email}', '${user.dob}', '${user.empId}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-link" onclick="deleteUser('${user._id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error loading users:', error);
                const tableBody = document.getElementById('users-table-body');
                tableBody.innerHTML = '<tr><td colspan="11" class="text-center text-danger">Error loading users</td></tr>';
            }
        }
// Add this after the existing loadUsers function
// Store users globally for category filtering
let allUsersData = [];

// Extend the existing loadUsers function to also populate categories
const originalLoadUsers = loadUsers;
loadUsers = async function() {
    await originalLoadUsers();
    
    try {
        const response = await fetch('/api/users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to fetch users for categories');
        }

        const users = await response.json();
        allUsersData = users;
        
        // Populate the category-based lists with default (role)
        populateCategoryLists(users, 'role');
        
    } catch (error) {
        console.error('Error loading users for categories:', error);
    }
};

// Add event listener for category selector after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const categorySelector = document.getElementById('categorySelector');
    if (categorySelector) {
        categorySelector.addEventListener('change', function() {
            populateCategoryLists(allUsersData, this.value);
        });
    }
});

// Populate the category-based lists
function populateCategoryLists(users, categoryType) {
    // Group users by selected category
    const categoryGroups = {};
    const categoryCounts = {};
    
    users.forEach(user => {
        let categoryValue;
        
        switch(categoryType) {
            case 'role':
                categoryValue = user.role || 'Unassigned';
                break;
            case 'location':
                categoryValue = user.location || 'Unassigned';
                break;
            case 'department':
                categoryValue = user.department || 'Unassigned';
                break;
            default:
                categoryValue = 'Unassigned';
        }
        
        if (!categoryGroups[categoryValue]) {
            categoryGroups[categoryValue] = [];
            categoryCounts[categoryValue] = 0;
        }
        
        categoryGroups[categoryValue].push(user);
        categoryCounts[categoryValue]++;
    });
    
    // Populate category summary
    const categorySummary = document.getElementById('category-summary');
    if (!categorySummary) return;
    
    categorySummary.innerHTML = '';
    
    const categories = Object.keys(categoryCounts).sort();
    
    // Create a summary card with counts
    const summaryHtml = `
        <div class="card bg-light">
            <div class="card-body">
                <h5 class="card-title">User Summary by ${categoryType.charAt(0).toUpperCase() + categoryType.slice(1)}</h5>
                <div class="row">
                    ${categories.map(category => `
                        <div class="col-md-3 col-sm-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span>${category}:</span>
                                <span class="badge ${categoryType === 'role' ? getRoleBadgeClass(category) : 'bg-secondary text-white'}">${categoryCounts[category]}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="mt-2">
                    <strong>Total Users:</strong> ${users.length}
                </div>
            </div>
        </div>
    `;
    
    categorySummary.innerHTML = summaryHtml;
    
    // Populate category accordion
    const categoryAccordion = document.getElementById('categoryAccordion');
    if (!categoryAccordion) return;
    
    categoryAccordion.innerHTML = '';
    
    categories.forEach((category, index) => {
        const categoryUsers = categoryGroups[category];
        const headingId = `heading${category.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '')}`;
        const collapseId = `collapse${category.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '')}`;
        const badgeClass = categoryType === 'role' ? getRoleBadgeClass(category) : 'bg-secondary text-white';
        
        const accordionItem = document.createElement('div');
        accordionItem.className = 'card';
        accordionItem.innerHTML = `
            <div class="card-header" id="${headingId}">
                <h2 class="mb-0">
                    <button class="btn btn-link btn-block text-left d-flex justify-content-between align-items-center" 
                            type="button" 
                            data-toggle="collapse" 
                            data-target="#${collapseId}" 
                            aria-expanded="${index === 0 ? 'true' : 'false'}" 
                            aria-controls="${collapseId}">
                        <span>${category}</span>
                        <span class="badge ${badgeClass}">${categoryUsers.length}</span>
                    </button>
                </h2>
            </div>
            <div id="${collapseId}" 
                 class="collapse ${index === 0 ? 'show' : ''}" 
                 aria-labelledby="${headingId}" 
                 data-parent="#categoryAccordion">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead>
                                <tr>
                                    <th>Emp. ID</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${categoryUsers.map(user => `
                                    <tr>
                                        <td>${user.empId || 'N/A'}</td>
                                        <td>${user.username}</td>
                                        <td>${user.email}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
        
        categoryAccordion.appendChild(accordionItem);
    });
}
        // Register new user
        async function registerUser(event) {
            event.preventDefault();

            const userData = {
                empId: document.getElementById("register-empId").value,
                username: document.getElementById("register-username").value,
                email: document.getElementById("register-email").value,
                dob: document.getElementById("register-dob").value,
                password: document.getElementById("register-password").value,
                role: document.getElementById("role").value,
                phone: document.getElementById("register-phone").value,
                location: document.getElementById("register-location").value,
                department: document.getElementById("register-department").value,
                position: document.getElementById("register-position").value,
                joinDate: getChicagoISOStringFromDateInput(document.getElementById("register-joinDate").value)
            };

            try {
                const response = await fetch("/api/register", {
                    method: "POST",
                    headers: { 
                        "Content-Type": "application/json",
                        "Authorization": `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify(userData)
                });

                if (response.ok) {
                    // Close the modal
                    $('#addUserModal').modal('hide');
                    
                    alert("User added successfully!");
                    document.getElementById("register-form").reset();
                    loadUsers(); // Reload the users table
                } else {
                    const error = await response.json();
                    alert(`Failed to add user: ${error.message}`);
                }
            } catch (error) {
                console.error("Error adding user:", error);
            }
        }

        // Delete user function
        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user?')) {
                return;
            }

            try {
                const response = await fetch(`/api/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to delete user');
                }

                alert('User deleted successfully');
                loadUsers(); // Reload the users list
            } catch (error) {
                console.error('Error deleting user:', error);
                alert('Failed to delete user');
            }
        }

        // Helper function for role badge styling
        function getRoleBadgeClass(role) {
            switch (role) {
                case 'Admin':
                    return 'bg-danger text-white';
                case 'Project Manager':
                    return 'bg-primary text-white';
                case 'Creative Service':
                    return 'bg-success text-white';
                case 'Editorial':
                    return 'bg-info text-white';
                default:
                    return 'bg-secondary text-white';
            }
        }

        // Add these new functions for editing
        async function editUser(userId, username, currentRole, email, dob, empId) {
            // Format the date for the input field
            const formattedDob = dob ? new Date(dob).toISOString().split('T')[0] : '';
            
            // Create modal HTML
            const modalHtml = `
                <div class="modal fade" id="editUserModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Edit User</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="editUserForm">
                                    <div class="mb-3">
                                        <label class="form-label">Employee ID</label>
                                        <input type="text" class="form-control" id="editEmpId" value="${empId || ''}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Username</label>
                                        <input type="text" class="form-control" id="editUsername" value="${username}" readonly>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" id="editEmail" value="${email || ''}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Date of Birth</label>
                                        <input type="date" class="form-control" id="editDob" value="${formattedDob}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">New Password (leave blank to keep current)</label>
                                        <input type="password" class="form-control" id="editPassword">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Role</label>
                                        <select class="form-control" id="editRole" required>
                                            <option value="Basic" ${currentRole === 'Basic' ? 'selected' : ''}>Basic</option>
                                            <option value="Admin" ${currentRole === 'Admin' ? 'selected' : ''}>Admin</option>
                                            <option value="Project Manager" ${currentRole === 'Project Manager' ? 'selected' : ''}>Project Manager</option>
                                            <option value="Creative Service" ${currentRole === 'Creative Service' ? 'selected' : ''}>Creative Service</option>
                                            <option value="Editorial" ${currentRole === 'Editorial' ? 'selected' : ''}>Editorial</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" onclick="saveUserChanges('${userId}')">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to document
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal using jQuery
            $('#editUserModal').modal('show');

            // Clean up modal after it's hidden
            $('#editUserModal').on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }

        async function saveUserChanges(userId) {
            const newPassword = document.getElementById('editPassword').value;
            const newRole = document.getElementById('editRole').value;
            const newEmail = document.getElementById('editEmail').value;
            const newDob = document.getElementById('editDob').value;
            const newEmpId = document.getElementById('editEmpId').value;

            try {
                const response = await fetch(`/api/users/${userId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: JSON.stringify({
                        password: newPassword || undefined,
                        role: newRole,
                        email: newEmail,
                        dob: newDob,
                        empId: newEmpId
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to update user');
                }

                $('#editUserModal').modal('hide');
                alert('User updated successfully');
                loadUsers();
            } catch (error) {
                console.error('Error updating user:', error);
                alert('Failed to update user');
            }
        }


document.addEventListener("DOMContentLoaded", () => {
  const userNameElement = document.querySelector(".user-name");
  const userRoleElement = document.querySelector(".user-role");

  // Retrieve the user's role and name from localStorage
  const userRole = localStorage.getItem("role");
  const userName = localStorage.getItem("username"); // Retrieve the username from localStorage

  // Update the user profile section
  if (userNameElement) {
    userNameElement.textContent = `Welcome, ${userName || "User"}`;
  }
  if (userRoleElement) {
    userRoleElement.textContent = userRole || "Role not found";
  }

  console.log("User Role:", userRole); // Debugging
  console.log("User Name:", userName); // Debugging
});


function goHome() {
  window.location.href = "index.html";
}

      

      function logout() {
            // Clear all user-related data
            const itemsToClear = ['token', 'username', 'role', 'userAvatar', 'userId'];
            itemsToClear.forEach(item => localStorage.removeItem(item));
            
            // Reset any user photos to default before redirecting
            const userPhotos = document.querySelectorAll('.user-photo');
            userPhotos.forEach(photo => {
                photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp';
            });
            
            // Redirect to login page
            window.location.href = '/login.html';
        }

    </script>

    <script>
        // Format date in CST/CDT (America/Chicago) as MM/DD/YYYY
        function formatDateCST(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '';
            const parts = new Intl.DateTimeFormat('en-US', {
                timeZone: 'America/Chicago',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }).formatToParts(date);
            const month = parts.find(p => p.type === 'month').value;
            const day = parts.find(p => p.type === 'day').value;
            const year = parts.find(p => p.type === 'year').value;
            return `${month}/${day}/${year}`;
        }

        // Function to convert date input to Chicago timezone ISO string
        function getChicagoISOStringFromDateInput(dateString) {
            try {
                if (!dateString) {
                    console.warn('Empty date string provided to getChicagoISOStringFromDateInput');
                    return null;
                }
                // dateString is 'YYYY-MM-DD'
                const result = luxon.DateTime.fromISO(dateString, { zone: 'America/Chicago' })
                    .startOf('day')
                    .toUTC()
                    .toISO();
                console.log('Date conversion:', dateString, '->', result);
                return result;
            } catch (error) {
                console.error('Error converting date:', error, 'Input:', dateString);
                return dateString; // Fallback to original string
            }
        }
    </script>

    <!-- Add this modal before the closing body tag -->
<div class="modal fade" id="importResultsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Results</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="import-success" class="alert alert-success"></div>
                <div id="import-errors" class="alert alert-danger" style="display: none;">
                    <h6>Errors:</h6>
                    <ul id="error-list"></ul>
                </div>
                <h6>Successfully Imported Users:</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Employee ID</th>
                                <th>Username</th>
                            </tr>
                        </thead>
                        <tbody id="imported-users-table"></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Add this to your existing script section, before the closing script tag -->
<script>
    // Function to download CSV template - Keep this outside as it's called from HTML
    function downloadTemplate() {
        const csvContent = "empId,username,email,password,dob,role,phone,location,department,position,joinDate\n" +
                          "EMP001,john.doe,<EMAIL>,password123,1990-01-01,Basic,1234567890,New York,IT,Developer,2022-01-01\n" +
                          "EMP002,jane.smith,<EMAIL>,password456,1985-05-15,Admin,0987654321,Chicago,HR,Manager,2021-06-15";
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.setAttribute('hidden', '');
        a.setAttribute('href', url);
        a.setAttribute('download', 'user_import_template.csv');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }
</script>
<script>
      // Add this function to update user info including avatar
      function updateUserInterface() {
          const userRole = localStorage.getItem('role');
          const userName = localStorage.getItem('username');
          const userAvatar = localStorage.getItem('userAvatar');
          
          // Update username and role
          const userNameElement = document.querySelector('.user-name');
          const userRoleElement = document.querySelector('.user-role');
          if (userNameElement) userNameElement.textContent = `Welcome, ${userName || 'User'}`;
          if (userRoleElement) userRoleElement.textContent = userRole || 'Role';
          
          // Update all user photos on the page
          const userPhotos = document.querySelectorAll('.user-photo');
          userPhotos.forEach(photo => {
              if (userAvatar) {
                  photo.src = userAvatar;
              } else {
                  photo.src = 'https://res.cloudinary.com/dcyeoe53a/image/upload/v1752579557/Photo_kdbjed.webp'; // Default photo
              }
          });
      }
      
      // Call this function when the page loads
      document.addEventListener('DOMContentLoaded', updateUserInterface);
      
      // Optional: Refresh user interface periodically to catch any changes
      setInterval(updateUserInterface, 30000); // Update every 30 seconds
      </script>
      <script>
        // Check if user is logged in
        document.addEventListener('DOMContentLoaded', () => {
          const token = localStorage.getItem('token');
          if (!token) {
            // No token found, redirect to login
            window.location.href = '/login.html';
          }
        });
      </script>


<!-- Add New User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> Add New User</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="register-form" onsubmit="registerUser(event)">
                    <div class="form-group">
                        <label for="register-empId">Employee ID</label>
                        <input type="text" class="form-control" id="register-empId" required />
                    </div>
                    <div class="form-group">
                        <label for="register-username">Username</label>
                        <input type="text" class="form-control" id="register-username" required />
                    </div>
                    <div class="form-group">
                        <label for="register-email">Email</label>
                        <input type="email" class="form-control" id="register-email" required />
                    </div>
                    <div class="form-group">
                        <label for="register-dob">Date of Birth</label>
                        <input type="date" class="form-control" id="register-dob" required />
                    </div>
                    <div class="form-group">
                        <label for="register-password">Password</label>
                        <input type="password" class="form-control" id="register-password" required />
                    </div>
                    <div class="form-group">
                        <label for="register-phone">Phone</label>
                        <input type="tel" class="form-control" id="register-phone" />
                    </div>
                    <div class="form-group">
                        <label for="register-location">Location</label>
                        <input type="text" class="form-control" id="register-location" />
                    </div>
                    <div class="form-group">
                        <label for="register-department">Department</label>
                        <input type="text" class="form-control" id="register-department" />
                    </div>
                    <div class="form-group">
                        <label for="register-position">Position</label>
                        <input type="text" class="form-control" id="register-position" />
                    </div>
                    <div class="form-group">
                        <label for="register-joinDate">Join Date</label>
                        <input type="date" class="form-control" id="register-joinDate" required />
                    </div>
                    <div class="form-group">
                        <label for="role">Role</label>
                        <select class="form-control" id="role" required>
                            <option value="Basic">Basic</option>
                            <option value="Admin">Admin</option>
                            <option value="Project Manager">Project Manager</option>
                            <option value="Creative Service">Creative Service</option>
                            <option value="Editorial">Editorial</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-plus"></i> Add User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-file-import"></i> Bulk Import Users</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulk-import-form" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="userFile">Upload CSV File</label>
                        <input type="file" class="form-control-file" id="userFile" name="userFile" accept=".csv" required>
                        <small class="form-text text-muted">
                            CSV must include: empId, username, email, password, role
                        </small>
                    </div>
                    <div class="form-group">
                        <button type="button" class="btn btn-link p-0" onclick="downloadTemplate()">
                            <i class="fas fa-download"></i> Download Template
                        </button>
                    </div>
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-upload"></i> Import Users
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

</body>
</html>
