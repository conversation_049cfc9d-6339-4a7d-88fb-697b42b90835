const mongoose = require('mongoose');
const models = require('./models');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/wipautomation', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function createTestWIPData() {
  try {
    console.log('🔧 Creating test WIP data for project 12345...');

    // Wait for connection to be ready
    await new Promise((resolve, reject) => {
      mongoose.connection.once('open', resolve);
      mongoose.connection.once('error', reject);
    });

    // Create WIP data for Level 1
    const wipData1 = {
      projectId: '12345-level-1',
      columnCounts: {
        'Column 1': {
          'Weighted Ratio (Yet to Start %)': 0,
          'Weighted Ratio (In Progress %)': 33.33,
          'Weighted Ratio (Completed %)': 66.67
        }
      }
    };

    await mongoose.connection.db.collection('projectwip').insertOne(wipData1);
    console.log('✅ WIP data for Level 1 created');

    // Create WIP data for Level 2
    const wipData2 = {
      projectId: '12345-level-2',
      columnCounts: {
        'Column 1': {
          'Weighted Ratio (Yet to Start %)': 100,
          'Weighted Ratio (In Progress %)': 0,
          'Weighted Ratio (Completed %)': 0
        }
      }
    };

    await mongoose.connection.db.collection('projectwip').insertOne(wipData2);
    console.log('✅ WIP data for Level 2 created');

    console.log('\n🎉 Test WIP data setup completed!');
    console.log('The charts should now display proper data.');

  } catch (error) {
    console.error('❌ Error creating test WIP data:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the creation
createTestWIPData();
