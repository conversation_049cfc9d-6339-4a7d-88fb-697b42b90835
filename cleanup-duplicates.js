const mongoose = require('mongoose');
const ProjectStatus = require('./models/ProjectStatus');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/wipautomation', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function cleanupDuplicateProjectStatuses() {
  try {
    console.log('🔍 Starting cleanup of duplicate project statuses...');
    
    // Find all project statuses
    const allStatuses = await ProjectStatus.find({}).sort({ createdAt: 1 });
    console.log(`📊 Found ${allStatuses.length} total project status documents`);
    
    // Group by base project ID (everything before -level-)
    const groupedByProject = {};
    
    allStatuses.forEach(status => {
      const baseProjectId = status.projectId.split('-level-')[0];
      if (!groupedByProject[baseProjectId]) {
        groupedByProject[baseProjectId] = [];
      }
      groupedByProject[baseProjectId].push(status);
    });
    
    console.log(`📊 Found ${Object.keys(groupedByProject).length} unique projects`);
    
    let duplicatesRemoved = 0;
    
    // Process each project
    for (const [baseProjectId, statuses] of Object.entries(groupedByProject)) {
      console.log(`\n🔍 Processing project: ${baseProjectId}`);
      console.log(`   Found ${statuses.length} status documents`);
      
      // Group by level identifier
      const levelGroups = {};
      
      statuses.forEach(status => {
        const levelMatch = status.projectId.match(/-level-(.+)$/);
        const levelId = levelMatch ? levelMatch[1] : 'unknown';
        
        if (!levelGroups[levelId]) {
          levelGroups[levelId] = [];
        }
        levelGroups[levelId].push(status);
      });
      
      // Check for duplicates within each level
      for (const [levelId, levelStatuses] of Object.entries(levelGroups)) {
        if (levelStatuses.length > 1) {
          console.log(`   ⚠️  Found ${levelStatuses.length} duplicates for level ${levelId}`);
          
          // Sort by creation date (keep the oldest one)
          levelStatuses.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
          
          const keepStatus = levelStatuses[0];
          const duplicatesToRemove = levelStatuses.slice(1);
          
          console.log(`   ✅ Keeping: ${keepStatus.projectId} (created: ${keepStatus.createdAt})`);
          
          // Remove duplicates
          for (const duplicate of duplicatesToRemove) {
            console.log(`   🗑️  Removing: ${duplicate.projectId} (created: ${duplicate.createdAt})`);
            await ProjectStatus.deleteOne({ _id: duplicate._id });
            duplicatesRemoved++;
          }
        } else {
          console.log(`   ✅ Level ${levelId}: No duplicates found`);
        }
      }
    }
    
    console.log(`\n🎉 Cleanup completed!`);
    console.log(`📊 Total duplicates removed: ${duplicatesRemoved}`);
    
    // Show final count
    const finalCount = await ProjectStatus.countDocuments();
    console.log(`📊 Remaining project status documents: ${finalCount}`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the cleanup
cleanupDuplicateProjectStatuses();
